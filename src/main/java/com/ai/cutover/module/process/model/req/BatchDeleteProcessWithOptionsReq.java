package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除流程定义请求（带选项）
 *
 * <AUTHOR>
 */
@Data
public class BatchDeleteProcessWithOptionsReq {

	/**
	 * 流程定义ID列表
	 */
	@NotEmpty(message = "流程定义ID列表不能为空")
	private List<Long> ids;

	/**
	 * 是否级联删除相关数据
	 */
	private Boolean cascade;

	/**
	 * 删除已部署版本后是否自动部署其他版本
	 * 默认为true，即自动部署最新可用版本
	 */
	private Boolean autoDeployOtherVersionFlag = true;

}
