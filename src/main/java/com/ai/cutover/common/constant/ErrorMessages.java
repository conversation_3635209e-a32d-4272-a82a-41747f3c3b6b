package com.ai.cutover.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 错误消息常量类 统一管理系统中的所有错误消息
 *
 * <AUTHOR>
 */
@UtilityClass
public class ErrorMessages {

	/**
	 * 通用错误消息
	 */
	@UtilityClass
	public static class Common {

		public static final String PARAM_INVALID = "参数无效: {}";

		public static final String PARAM_MISSING = "参数缺失";

		public static final String DATA_NOT_FOUND = "数据不存在";

		public static final String DATA_ALREADY_EXISTS = "数据已存在";

		public static final String OPERATION_FAILED = "操作失败: {}";

		public static final String PERMISSION_DENIED = "权限不足";

		public static final String SYSTEM_ERROR = "系统错误: {}";

		public static final String NETWORK_ERROR = "网络错误: {}";

		public static final String TIMEOUT_ERROR = "操作超时: {}";

		public static final String CONCURRENT_ERROR = "并发操作冲突: {}";

		public static final String CANNOT_DELETE_BUILTIN = "不能删除内置数据: {}";

		public static final String CANNOT_MODIFY_BUILTIN = "不能修改内置数据: {}";

	}

	/**
	 * 通用错误消息
	 */
	@UtilityClass
	public static class General {

		public static final String PARAM_NOT_NULL = "参数不能为空";

		public static final String PARAM_NOT_BLANK = "参数不能为空字符串";

		public static final String ID_NOT_NULL = "ID不能为空";

	}

	/**
	 * 用户相关错误消息
	 */
	@UtilityClass
	public static class User {

		// 参数校验
		public static final String USERNAME_NOT_BLANK = "用户名不能为空";

		public static final String PASSWORD_NOT_BLANK = "密码不能为空";

		public static final String EMAIL_NOT_BLANK = "邮箱不能为空";

		public static final String PHONE_NOT_BLANK = "手机号不能为空";

		// 业务错误
		public static final String USER_NOT_FOUND = "用户不存在: {}";

		public static final String USER_ALREADY_EXISTS = "用户已存在: {}";

		public static final String USERNAME_ALREADY_EXISTS = "用户名已存在: {}";

		public static final String EMAIL_ALREADY_EXISTS = "邮箱已存在: {}";

		public static final String PHONE_ALREADY_EXISTS = "手机号已存在: {}";

		public static final String USER_DISABLED = "用户已被禁用: {}";

		public static final String USER_LOCKED = "用户已被锁定: {}";

		public static final String USER_EXPIRED = "用户已过期: {}";

		public static final String PASSWORD_NOT_MATCH = "密码不匹配";

		public static final String OLD_PASSWORD_ERROR = "原密码错误";

		public static final String PASSWORD_TOO_WEAK = "密码强度不足";

		public static final String PASSWORD_EXPIRED = "密码已过期";

		public static final String LOGIN_FAILED = "登录失败: {}";

		public static final String LOGIN_RETRY_LIMIT_EXCEEDED = "登录重试次数超限";

		public static final String USER_NOT_LOGIN = "用户未登录";

		public static final String USER_SESSION_EXPIRED = "用户会话已过期";

		public static final String USERNAME_REQUIRED = "用户名不能为空";

		public static final String PASSWORD_REQUIRED = "密码不能为空";

		public static final String REAL_NAME_REQUIRED = "真实姓名不能为空";

		public static final String PASSWORD_CONFIRM_NOT_MATCH = "两次密码输入不一致";

		public static final String CAPTCHA_ERROR = "验证码错误";

		public static final String USER_DISABLED_OR_LOCKED = "用户已被禁用或锁定";

		public static final String USERNAME_OR_PASSWORD_ERROR = "用户名或密码错误";

		public static final String ACCOUNT_LOCKED = "账户已被锁定，请{}分钟后再试";

		public static final String LOGIN_FAIL_COUNT_WARNING = "登录失败{}次，还有{}次机会";

		public static final String CAPTCHA_REQUIRED = "验证码不能为空";

		public static final String CAPTCHA_EXPIRED = "验证码已过期或不存在";

		public static final String CAPTCHA_INVALID = "验证码错误";

		public static final String PASSWORD_SAME_AS_OLD = "新密码不能与原密码相同";

		public static final String PASSWORD_TOO_SHORT = "密码长度不能少于{}位";

		public static final String PASSWORD_TOO_LONG = "密码长度不能超过{}位";

		public static final String PASSWORD_INVALID_FORMAT = "密码格式不正确，必须包含字母和数字";

	}

	/**
	 * 角色相关错误消息
	 */
	@UtilityClass
	public static class Role {

		// 参数校验
		public static final String ROLE_CODE_NOT_BLANK = "角色编码不能为空";

		public static final String ROLE_NAME_NOT_BLANK = "角色名称不能为空";

		// 业务错误
		public static final String ROLE_NOT_FOUND = "角色不存在: {}";

		public static final String ROLE_ALREADY_EXISTS = "角色已存在: {}";

		public static final String ROLE_CODE_ALREADY_EXISTS = "角色编码已存在: {}";

		public static final String ROLE_NAME_ALREADY_EXISTS = "角色名称已存在: {}";

		public static final String ROLE_HAS_USERS = "角色[{}]下存在{}个用户，无法删除";

		public static final String ROLE_DISABLED = "角色[{}]已被禁用";

		public static final String CANNOT_DELETE_BUILTIN_ROLE = "不能删除内置角色[{}]";

		public static final String CANNOT_MODIFY_BUILTIN_ROLE = "不能修改内置角色[{}]";

	}

	/**
	 * 部门相关错误消息
	 */
	@UtilityClass
	public static class Dept {

		// 参数校验
		public static final String DEPT_CODE_NOT_BLANK = "部门编码不能为空";

		public static final String DEPT_NAME_NOT_BLANK = "部门名称不能为空";

		// 业务错误
		public static final String DEPT_NOT_FOUND = "部门不存在: {}";

		public static final String DEPT_ALREADY_EXISTS = "部门已存在: {}";

		public static final String DEPT_CODE_ALREADY_EXISTS = "部门编码已存在: {}";

		public static final String DEPT_NAME_ALREADY_EXISTS = "部门名称已存在: {}";

		public static final String DEPT_HAS_CHILDREN = "部门[{}]存在{}个子部门，无法删除";

		public static final String DEPT_HAS_USERS = "部门[{}]存在{}个用户，无法删除";

		public static final String DEPT_DISABLED = "部门已被禁用";

		public static final String PARENT_DEPT_NOT_FOUND = "父部门不存在";

		public static final String CANNOT_SET_SELF_AS_PARENT = "不能将自己设置为父部门";

		public static final String CANNOT_SET_CHILD_AS_PARENT = "不能将子部门设置为父部门";

		public static final String DEPT_LEVEL_TOO_DEEP = "部门层级过深";

	}

	/**
	 * 菜单相关错误消息
	 */
	@UtilityClass
	public static class Menu {

		// 参数校验
		public static final String MENU_NAME_NOT_BLANK = "菜单名称不能为空";

		public static final String MENU_TYPE_NOT_BLANK = "菜单类型不能为空";

		// 业务错误
		public static final String MENU_NOT_FOUND = "菜单不存在: {}";

		public static final String MENU_ALREADY_EXISTS = "菜单已存在: {}";

		public static final String MENU_NAME_ALREADY_EXISTS = "菜单名称已存在: {}";

		public static final String MENU_PATH_ALREADY_EXISTS = "菜单路径已存在: {}";

		public static final String MENU_PERMISSION_ALREADY_EXISTS = "菜单权限标识已存在: {}";

		public static final String MENU_HAS_CHILDREN = "菜单[{}]存在{}个子菜单，无法删除";

		public static final String MENU_DISABLED = "菜单已被禁用";

		public static final String PARENT_MENU_NOT_FOUND = "父菜单不存在";

		public static final String CANNOT_SET_SELF_AS_PARENT = "不能将自己设置为父菜单";

		public static final String CANNOT_SET_CHILD_AS_PARENT = "不能将子菜单设置为父菜单";

		public static final String MENU_TYPE_INVALID = "菜单类型无效";

	}

	/**
	 * 权限相关错误消息
	 */
	@UtilityClass
	public static class Permission {

		// 参数校验
		public static final String PERMISSION_CODE_NOT_BLANK = "权限编码不能为空";

		public static final String PERMISSION_NAME_NOT_BLANK = "权限名称不能为空";

		// 业务错误
		public static final String PERMISSION_NOT_FOUND = "权限不存在: {}";

		public static final String PERMISSION_ALREADY_EXISTS = "权限已存在: {}";

		public static final String PERMISSION_CODE_ALREADY_EXISTS = "权限编码已存在: {}";

		public static final String PERMISSION_NAME_ALREADY_EXISTS = "权限名称已存在: {}";

		public static final String PERMISSION_DISABLED = "权限已被禁用";

		public static final String PERMISSION_TYPE_INVALID = "权限类型无效";

		public static final String CANNOT_DELETE_BUILTIN_PERMISSION = "不能删除内置权限";

		public static final String CANNOT_MODIFY_BUILTIN_PERMISSION = "不能修改内置权限";

	}

	/**
	 * 流程相关错误消息
	 */
	@UtilityClass
	public static class Process {

		// 参数校验
		public static final String PROCESS_KEY_NOT_BLANK = "流程Key不能为空";

		public static final String PROCESS_NAME_NOT_BLANK = "流程名称不能为空";

		public static final String BPMN_XML_NOT_BLANK = "BPMN XML不能为空";

		public static final String PROCESS_DEFINITION_ID_NOT_NULL = "流程定义ID不能为空";

		public static final String PROCESS_DEFINITION_VERSION_NOT_NULL = "流程定义版本号不能为空";

		public static final String SOURCE_PROCESS_KEY_NOT_BLANK = "源流程Key不能为空";

		public static final String NEW_PROCESS_KEY_NOT_BLANK = "新流程Key不能为空";

		public static final String NEW_PROCESS_NAME_NOT_BLANK = "新流程名称不能为空";

		// 业务错误
		public static final String PROCESS_NOT_FOUND = "流程不存在";

		public static final String PROCESS_ALREADY_EXISTS = "流程已存在";

		public static final String PROCESS_KEY_ALREADY_EXISTS = "流程Key已存在";

		public static final String PROCESS_DEPLOYED = "流程已部署，无法修改";

		public static final String PROCESS_NOT_DEPLOYED = "流程未部署";

		public static final String PROCESS_INSTANCE_NOT_FOUND = "流程实例不存在: {}";

		public static final String PROCESS_TASK_NOT_FOUND = "流程任务不存在";

		public static final String PROCESS_DEFINITION_INVALID = "流程定义无效";

		public static final String BPMN_XML_INVALID = "BPMN XML格式无效: {}";

		public static final String BPMN_XML_PARSE_FAILED = "BPMN XML解析失败: {}";

		public static final String BPMN_XML_FORMAT_ERROR = "BPMN XML格式错误: {}";

		public static final String BPMN_XML_VALIDATION_FAILED = "BPMN XML验证失败: {}";

		public static final String PROCESS_START_FAILED = "流程启动失败: {}";

		public static final String PROCESS_COMPLETE_FAILED = "流程完成失败";

		// 流程操作相关错误消息
		public static final String TASK_COMPLETE_FAILED = "完成任务失败: {}";

		public static final String TASK_APPROVAL_FAILED = "审批任务失败: {}";

		public static final String USER_TASKS_QUERY_FAILED = "查询用户待办任务失败: {}";

		public static final String PROCESS_TASKS_QUERY_FAILED = "查询流程任务失败: {}";

		public static final String PROCESS_INSTANCE_QUERY_FAILED = "查询流程实例失败: {}";

		public static final String PROCESS_HISTORY_QUERY_FAILED = "查询流程历史失败: {}";

		public static final String PROCESS_TERMINATE_FAILED = "终止流程失败: {}";

		public static final String PROCESS_SUSPEND_FAILED = "挂起流程失败: {}";

		public static final String PROCESS_ACTIVATE_FAILED = "激活流程失败: {}";

		public static final String PROCESS_VARIABLES_QUERY_FAILED = "查询流程变量失败: {}";

		public static final String PROCESS_VARIABLES_SET_FAILED = "设置流程变量失败: {}";

		public static final String TASK_NOT_FOUND = "任务不存在: {}";

		public static final String TASK_INFO_QUERY_FAILED = "查询任务详情失败: {}";

		public static final String TASK_DELEGATE_FAILED = "委派任务失败: {}";

		public static final String TASK_ASSIGN_FAILED = "转办任务失败: {}";

		// 流程设计相关错误消息
		public static final String PROCESS_DEFINITION_MISSING_BPMN = "流程定义缺少BPMN XML内容";

		public static final String PROCESS_DEPLOY_FAILED = "部署失败: {}";

		public static final String PROCESS_UNDEPLOY_FAILED = "取消部署失败: {}";

		public static final String PROCESS_EXTRACT_KEY_FAILED = "无法从BPMN XML中解析流程Key";

		public static final String PROCESS_EXTRACT_KEY_ERROR = "提取流程Key失败: {}";

		public static final String PROCESS_EXTRACT_NAME_FAILED = "提取流程名称失败: {}";

		public static final String PROCESS_DEFINITION_VERSION_NOT_FOUND = "指定版本的流程定义不存在";

	}

	/**
	 * 割接相关错误消息
	 */
	@UtilityClass
	public static class Cutover {

		public static final String CUTOVER_ORDER_NOT_FOUND = "割接工单不存在";

		public static final String CUTOVER_ORDER_ALREADY_EXISTS = "割接工单已存在";

		public static final String CUTOVER_TIME_CONFLICT = "割接时间冲突";

		public static final String CUTOVER_STATUS_INVALID = "割接状态无效";

		public static final String CUTOVER_CANNOT_MODIFY = "割接工单不能修改";

		public static final String CUTOVER_CANNOT_DELETE = "割接工单不能删除";

		public static final String CUTOVER_RESOURCE_CONFLICT = "割接资源冲突";

		public static final String CUTOVER_APPROVAL_FAILED = "割接审批失败";

		// 参数校验相关错误消息
		public static final String BUSINESS_KEY_REQUIRED = "业务Key不能为空";

		public static final String INITIATOR_ID_REQUIRED = "发起人ID不能为空";

		public static final String CUTOVER_PLAN_REQUIRED = "割接方案不能为空";

		public static final String AFFECTED_SYSTEMS_REQUIRED = "影响系统不能为空";

		public static final String EMERGENCY_REASON_REQUIRED = "紧急原因不能为空";

		public static final String EMERGENCY_LEVEL_REQUIRED = "紧急级别不能为空";

	}

	/**
	 * 系统异常相关错误消息
	 */
	@UtilityClass
	public static class System {

		public static final String PARAM_VALIDATION_FAILED = "参数校验失败: {}";

		public static final String PARAM_ERROR = "参数错误: {}";

		public static final String USER_NOT_LOGIN = "用户未登录";

		public static final String SYSTEM_INTERNAL_ERROR = "系统内部错误";

		public static final String SYSTEM_INTERNAL_ERROR_WITH_MESSAGE = "系统内部错误: {}";

		public static final String DATA_OPERATION_FAILED = "数据操作失败，可能存在重复数据或违反约束条件";

		public static final String DATA_ALREADY_EXISTS_CHECK = "数据已存在，请检查后重试";

		public static final String DATA_ACCESS_FAILED = "数据访问失败，请稍后重试";

	}

	/**
	 * 文件相关错误消息
	 */
	@UtilityClass
	public static class File {

		public static final String FILE_NOT_FOUND = "文件不存在";

		public static final String FILE_UPLOAD_FAILED = "文件上传失败";

		public static final String FILE_DOWNLOAD_FAILED = "文件下载失败";

		public static final String FILE_DELETE_FAILED = "文件删除失败";

		public static final String FILE_SIZE_EXCEEDED = "文件大小超限";

		public static final String FILE_TYPE_NOT_SUPPORTED = "文件类型不支持";

		public static final String FILE_NAME_INVALID = "文件名无效";

		public static final String FILE_PATH_INVALID = "文件路径无效";

	}

	/**
	 * 数据库相关错误消息
	 */
	@UtilityClass
	public static class Database {

		public static final String CONNECTION_FAILED = "数据库连接失败";

		public static final String QUERY_FAILED = "数据库查询失败";

		public static final String UPDATE_FAILED = "数据库更新失败";

		public static final String INSERT_FAILED = "数据库插入失败";

		public static final String DELETE_FAILED = "数据库删除失败";

		public static final String TRANSACTION_FAILED = "数据库事务失败";

		public static final String CONSTRAINT_VIOLATION = "数据库约束违反";

		public static final String DUPLICATE_KEY = "数据库主键重复";

	}

	/**
	 * 缓存相关错误消息
	 */
	@UtilityClass
	public static class Cache {

		public static final String CACHE_GET_FAILED = "缓存获取失败: key={}";

		public static final String CACHE_SET_FAILED = "缓存设置失败: key={}, value={}";

		public static final String CACHE_SET_WITH_TIMEOUT_FAILED = "缓存设置失败: key={}, value={}, timeout={}, unit={}";

		public static final String CACHE_SET_WITH_DURATION_FAILED = "缓存设置失败: key={}, value={}, duration={}";

		public static final String CACHE_DELETE_FAILED = "缓存删除失败: key={}";

		public static final String CACHE_DELETE_BATCH_FAILED = "批量缓存删除失败: keys={}";

		public static final String CACHE_CLEAR_FAILED = "缓存清空失败";

		public static final String CACHE_CONNECTION_FAILED = "缓存连接失败";

		public static final String CACHE_HAS_KEY_FAILED = "缓存键存在性检查失败: key={}";

		public static final String CACHE_EXPIRE_FAILED = "缓存过期时间设置失败: key={}, timeout={}, unit={}";

		public static final String CACHE_GET_EXPIRE_FAILED = "缓存过期时间获取失败: key={}";

		public static final String CACHE_KEYS_FAILED = "缓存键匹配失败: pattern={}";

		public static final String CACHE_INCREMENT_FAILED = "递增失败: key={}, delta={}";

		public static final String CACHE_DECREMENT_FAILED = "递减失败: key={}, delta={}";

		public static final String CACHE_TYPE_CONVERSION_FAILED = "类型转换失败: key={}, value={}, valueType={}, targetType={}";

		public static final String CACHE_TYPE_NOT_SUPPORTED = "无法将缓存值转换为目标类型: key={}, valueType={}, targetType={}";

	}

	/**
	 * 配置相关错误消息
	 */
	@UtilityClass
	public static class Config {

		public static final String CONFIG_NOT_FOUND = "配置不存在";

		public static final String CONFIG_INVALID = "配置无效";

		public static final String CONFIG_LOAD_FAILED = "配置加载失败";

		public static final String CONFIG_SAVE_FAILED = "配置保存失败";

		public static final String CONFIG_FORMAT_ERROR = "配置格式错误";

	}

}
